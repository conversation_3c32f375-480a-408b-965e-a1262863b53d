"""
Unified Data Scheduler Configuration
"""

import json
import os
from typing import Dict, List, Any
try:
    from logger import logger
except ImportError:
    from ..config import get_logger
    logger = get_logger()

# Default configuration for unified data scheduler
DEFAULT_CONFIG = {
    "scheduler": {
        "timezone": "Asia/Shanghai",
        "max_workers": 4,
        "function_delay": 5,  # seconds between functions
        "category_delay": 10,  # seconds between categories
        "error_retry_count": 3,
        "error_retry_delay": 30
    },
    "data_types": {
        "user": {
            "enabled": True,
            "description": "User data collection (followers, dynamics, videos)",
            "functions": {
                "minute": [
                    # Add minute-level user data functions if needed
                ],
                "hourly": [
                    "fetch_user_current_stat"
                ],
                "daily": [
                    "fetch_user_dynamics",
                    "fetch_all_video",
                    "fetch_dahanghai_list",
                    "fetch_followers_list",
                    "fetch_follower_review"
                ],
                "three_day": [
                    "gen_recent_relationships_30",
                    "gen_recent_relationships_100",
                    "gen_comment_topics",
                    "summarise_rise_reason"
                ],
                "weekly": [
                    "fetch_fans_medal_rank",
                    "fetch_user_info"
                ],
                "monthly": [
                    "fetch_history_follower_and_dahanghai"
                ],
                "till": [
                    "fetch_all_dynamics_comments",
                    "fetch_all_videos_comments",
                    "gen_comment_sensiment",
                    "fetch_tieba_threads",
                    "fetch_tieba_whole"
                ]
            },
            "schedule": {
                "minute": {"interval": 1, "jitter": 30},  # every minute
                "hourly": {"interval": 1, "jitter": 60},  # every hour
                "daily": {"hour": 0, "minute": 0, "jitter": 120},  # daily at midnight
                "three_day": {"day": "*/3", "hour": 0, "minute": 0, "jitter": 120},  # every 3 days
                "weekly": {"day_of_week": "mon", "hour": 3, "minute": 0, "jitter": 300},  # Monday 3:00 AM
                "monthly": {"day": 1, "hour": 0, "minute": 0, "jitter": 600},  # 1st day of month
                "till": {"interval": 5, "mode": "idle_check", "jitter": 30}  # every 5 minutes when idle
            },
            "limiters": {
                "hourly_task_limit": 1,
                "daily_task_limit": 1,
                "long_running_task_limit": 1,
                "till_worker_limit": 1
            }
        },
        "creator": {
            "enabled": True,
            "description": "Creator data collection (existing creator_info_server functions)",
            "functions": {
                "daily": [
                    # These will be loaded from creator_scheduler_config.py
                ]
            },
            "schedule": {
                "daily": {"hour": 2, "minute": 30}  # daily at 2:30 AM
            }
        },
        "live": {
            "enabled": True,
            "description": "Live streaming data collection (room status, danmaku)",
            "functions": {
                "continuous": [
                    "monitor_live_rooms",
                    "collect_danmaku_data"
                ],
                "minute": [
                    "update_live_status"
                ]
            },
            "schedule": {
                "continuous": {"mode": "continuous"},  # always running
                "minute": {"interval": 1}  # every minute
            }
        }
    }
}

def get_config_file_path():
    """Get the path to the configuration file"""
    return os.path.join(os.path.dirname(__file__), "unified_scheduler_config.json")

def load_unified_config():
    """
    Load unified scheduler configuration
    """
    config_file = get_config_file_path()
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"Loaded unified scheduler config from {config_file}")
                return config
        else:
            logger.info(f"Config file not found, creating default config at {config_file}")
            save_unified_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG.copy()
    except Exception as e:
        logger.error(f"Error loading unified scheduler config: {e}")
        return DEFAULT_CONFIG.copy()

def save_unified_config(config: Dict[str, Any]):
    """
    Save unified scheduler configuration
    """
    config_file = get_config_file_path()
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved unified scheduler config to {config_file}")
    except Exception as e:
        logger.error(f"Error saving unified scheduler config: {e}")

def get_enabled_data_types():
    """
    Get list of enabled data types
    """
    config = load_unified_config()
    enabled_types = []
    
    for data_type, settings in config.get("data_types", {}).items():
        if settings.get("enabled", False):
            enabled_types.append(data_type)
    
    return enabled_types

def get_data_type_functions(data_type: str):
    """
    Get functions for a specific data type
    """
    config = load_unified_config()
    data_types = config.get("data_types", {})
    
    if data_type not in data_types:
        logger.error(f"Unknown data type: {data_type}")
        return {}
    
    return data_types[data_type].get("functions", {})

def get_data_type_schedule(data_type: str):
    """
    Get schedule configuration for a specific data type
    """
    config = load_unified_config()
    data_types = config.get("data_types", {})
    
    if data_type not in data_types:
        logger.error(f"Unknown data type: {data_type}")
        return {}
    
    return data_types[data_type].get("schedule", {})

def get_scheduler_config():
    """
    Get general scheduler configuration
    """
    config = load_unified_config()
    return config.get("scheduler", {})

def update_data_type_status(data_type: str, enabled: bool):
    """
    Update the enabled status of a data type
    """
    config = load_unified_config()
    
    if data_type in config.get("data_types", {}):
        config["data_types"][data_type]["enabled"] = enabled
        save_unified_config(config)
        logger.info(f"Updated {data_type} enabled status to {enabled}")
    else:
        logger.error(f"Unknown data type: {data_type}")

if __name__ == "__main__":
    config = load_unified_config()
    print("Unified scheduler configuration loaded successfully")
    print(f"Enabled data types: {get_enabled_data_types()}")
