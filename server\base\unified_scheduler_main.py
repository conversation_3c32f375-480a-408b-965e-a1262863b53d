"""
Unified Data Scheduler Main Entry Point
"""

import asyncio
import signal
import sys
import time
import multiprocessing
from typing import List, Optional

from logger import logger
from unified_data_scheduler import UnifiedDataScheduler
from unified_scheduler_config import get_enabled_data_types, update_data_type_status


class UnifiedSchedulerManager:
    """
    Unified Scheduler Manager
    Manages the lifecycle of the unified data scheduler
    """

    def __init__(self, data_types: List[str] = None, uid: str = "401315430"):
        """
        Initialize scheduler manager
        
        Args:
            data_types: List of data types to enable ['user', 'creator', 'live', 'all']
            uid: User ID for creator data
        """
        self.data_types = data_types or get_enabled_data_types()
        self.uid = uid
        self.scheduler = None
        self.is_running = False
        
        logger.info(f"UnifiedSchedulerManager initialized with data types: {self.data_types}")

    async def initialize_and_start(self):
        """Initialize and start the unified scheduler"""
        try:
            logger.info(f"Initializing Unified Data Scheduler for data types: {self.data_types}")
            
            # Create scheduler instance
            self.scheduler = UnifiedDataScheduler(data_types=self.data_types, uid=self.uid)
            
            # Initialize
            await self.scheduler.initialize()
            
            # Start scheduler
            self.scheduler.start_scheduler()
            
            self.is_running = True
            logger.info("Unified Data Scheduler started successfully")
            
            # Keep running
            while self.is_running:
                await asyncio.sleep(60)  # Check every minute
                
        except Exception as e:
            logger.error(f"Error in unified scheduler manager: {e}")
            raise

    def stop(self):
        """Stop the scheduler"""
        if self.scheduler:
            self.scheduler.stop_scheduler()
        self.is_running = False
        logger.info("Unified Scheduler Manager stopped")

    def get_status(self):
        """Get status"""
        if self.scheduler:
            return self.scheduler.get_scheduler_status()
        return {"status": "not_initialized", "jobs": [], "data_types": self.data_types}


def signal_handler(signum, frame):
    """Signal handler for graceful shutdown"""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


async def run_once(data_types: List[str] = None, uid: str = "401315430"):
    """
    Run scheduler once (for testing)
    """
    logger.info("Running unified scheduler once...")
    
    try:
        scheduler = UnifiedDataScheduler(data_types=data_types, uid=uid)
        await scheduler.initialize()
        
        # Execute tasks based on data types
        if not data_types or 'user' in data_types or 'all' in data_types:
            await scheduler.execute_user_functions_safely('daily')
        
        if not data_types or 'creator' in data_types or 'all' in data_types:
            await scheduler.execute_creator_functions_safely()
        
        if not data_types or 'live' in data_types or 'all' in data_types:
            await scheduler.execute_live_monitoring()
        
        logger.info("One-time execution completed successfully")
        
    except Exception as e:
        logger.error(f"Error in one-time execution: {e}")
        raise


def show_status():
    """Show current scheduler status"""
    try:
        # Try to read status from a status file or process
        print("=== Unified Data Scheduler Status ===")
        print(f"Enabled data types: {get_enabled_data_types()}")
        
        # Check if scheduler is running (simplified check)
        import psutil
        running_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'unified_scheduler_main.py' in cmdline:
                    running_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if running_processes:
            print("Running processes:")
            for proc in running_processes:
                print(f"  PID: {proc['pid']}, Command: {proc['cmdline']}")
        else:
            print("No unified scheduler processes found")
            
    except Exception as e:
        logger.error(f"Error showing status: {e}")
        print(f"Error: {e}")


def worker(data_types: List[str] = None, uid: str = "401315430"):
    """Worker process for the scheduler"""
    while True:
        try:
            async def main_worker():
                manager = UnifiedSchedulerManager(data_types=data_types, uid=uid)
                await manager.initialize_and_start()
            
            asyncio.run(main_worker())
            
        except Exception as e:
            logger.error(f"Worker encountered an error: {e}")
            time.sleep(5)  # Wait before restarting


def monitor(data_types: List[str] = None, uid: str = "401315430"):
    """Monitor and restart worker process if it crashes"""
    logger.info("Starting unified scheduler in monitor mode...")
    
    while True:
        try:
            p = multiprocessing.Process(target=worker, args=(data_types, uid))
            p.start()
            
            while p.is_alive():
                p.join(timeout=10)
            
            logger.warning("Worker process ended, restarting...")
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping monitor...")
            if p.is_alive():
                p.terminate()
                p.join()
            break
        except Exception as e:
            logger.error(f"Monitor encountered an error: {e}")
            time.sleep(5)


async def main():
    """Main function for direct execution"""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create manager
    manager = UnifiedSchedulerManager()
    
    try:
        await manager.initialize_and_start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
    finally:
        manager.stop()


def print_help():
    """Print help information"""
    help_text = """
Unified Data Scheduler

Usage:
    python unified_scheduler_main.py [options]

Options:
    --mode MODE         运行模式 (monitor|once|status|help) [default: monitor]
    --data-types TYPES  数据类型，逗号分隔 (user,creator,live,all) [default: all enabled types]
    --uid UID          用户UID [default: 401315430]
    --help             显示帮助信息

Data Types:
    user     - 用户数据 (粉丝数、动态、视频等)
    creator  - 创作者数据 (现有creator_info_server功能)
    live     - 直播数据 (直播状态、弹幕等)
    all      - 所有数据类型

Examples:
    python unified_scheduler_main.py --mode monitor
    python unified_scheduler_main.py --mode once --data-types user,creator
    python unified_scheduler_main.py --mode status
    """
    print(help_text)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Unified Data Scheduler')
    parser.add_argument('--mode', choices=['monitor', 'once', 'status', 'help'], 
                       default='monitor', help='运行模式')
    parser.add_argument('--data-types', type=str, help='数据类型，逗号分隔 (user,creator,live,all)')
    parser.add_argument('--uid', default='401315430', help='用户UID')
    
    args = parser.parse_args()
    
    # Parse data types
    data_types = None
    if args.data_types:
        data_types = [dt.strip() for dt in args.data_types.split(',')]
    
    if args.mode == 'help':
        print_help()
    elif args.mode == 'monitor':
        logger.info("Starting in monitor mode...")
        monitor(data_types=data_types, uid=args.uid)
    elif args.mode == 'once':
        logger.info("Running once...")
        asyncio.run(run_once(data_types=data_types, uid=args.uid))
    elif args.mode == 'status':
        show_status()
    else:
        print_help()
