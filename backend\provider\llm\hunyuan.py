# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-
# @Date    : 2024/07/15 15:37
# <AUTHOR> yuymf
# @Desc    :

import json
import types
from typing import Any, Dict, Iterator, List, Optional
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import (
    TencentCloudSDKException,
)
from tencentcloud.hunyuan.v20230901 import hunyuan_client, models

from langchain_core.callbacks.manager import CallbackManagerForLLMRun
from langchain_core.language_models.llms import LLM
from langchain_core.outputs import GenerationChunk


class HunyuanChatModel:

    def __init__(self, SecretId, SecretKey) -> types.NoneType:
        self.SecretId = SecretId
        self.SecretKey = SecretKey

    async def get_chat_completetion(self, content, streaming=True):
        # Ref: <PERSON>nyuan <PERSON>, https://cloud.tencent.com/document/product/1278/85305
        try:
            cred = credential.Credential(self.SecretId, self.SecretKey)
            httpProfile = HttpProfile()
            httpProfile.endpoint = "hunyuan.tencentcloudapi.com"

            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            client = hunyuan_client.HunyuanClient(cred, "", clientProfile)

            req = models.ChatCompletionsRequest()
            params = {
                "Model": "hunyuan-pro",
                "Messages": [{"Role": "user", "Content": content}],
            }
            req.from_json_string(json.dumps(params))

            resp = client.ChatCompletions(req)
            if isinstance(resp, types.GeneratorType) and streaming:
                for event in resp:
                    return event
            else:
                return resp

        except TencentCloudSDKException as err:
            return f"Error Happens: {err}"


class HunyuanChatLCModel(LLM):
    """
    Used for HunyuanChatLCModel.invoke("") ...
    """

    SecretId: str
    SecretKey: str

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Run the LLM on the given input.

        Override this method to implement the LLM logic.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of the stop substrings.
                If stop tokens are not supported consider raising NotImplementedError.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            The model output as a string. Actual completions SHOULD NOT include the prompt.
        """
        if stop is not None:
            raise ValueError("stop kwargs are not permitted.")
        try:
            cred = credential.Credential(self.SecretId, self.SecretKey)
            httpProfile = HttpProfile()
            httpProfile.endpoint = "hunyuan.tencentcloudapi.com"

            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            client = hunyuan_client.HunyuanClient(cred, "", clientProfile)

            req = models.ChatCompletionsRequest()
            params = {
                "Model": "hunyuan-pro",
                "Messages": [{"Role": "user", "Content": prompt}],
            }
            req.from_json_string(json.dumps(params))

            content = client.ChatCompletions(req).Choices[0].Message.Content
            return content

        except TencentCloudSDKException as err:
            print(err)

    def _stream(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[GenerationChunk]:
        """Stream the LLM on the given prompt.

        This method should be overridden by subclasses that support streaming.

        If not implemented, the default behavior of calls to stream will be to
        fallback to the non-streaming version of the model and return
        the output as a single chunk.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of these substrings.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            An iterator of GenerationChunks.
        """
        resp = self._call(prompt, stop, run_manager, **kwargs)
        for char in resp:
            chunk = GenerationChunk(text=char)
            if run_manager:
                run_manager.on_llm_new_token(chunk.text, chunk=chunk)

            yield chunk

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """Return a dictionary of identifying parameters."""
        return {
            # The model name allows users to specify custom token counting
            # rules in LLM monitoring applications (e.g., in LangSmith users
            # can provide per token pricing for their model and monitor
            # costs for the given LLM.)
            "model_name": "HunyuanChatLCModel",
        }

    @property
    def _llm_type(self) -> str:
        """Get the type of language model used by this chat model. Used for logging purposes only."""
        return "custom"