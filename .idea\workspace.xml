<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5d038f75-6280-47d5-ac96-4a74e9cd67e4" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/algos/prompts/livestream_chat.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/algos/provider/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/algos/provider/llm/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/COOKIE.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/SERVER.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/__main__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/cookie_adapter.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/cookie_cli.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/cookie_manager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/cookie_refresh.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/unified_scheduler_config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/base/unified_scheduler_main.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/config/cookie_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/config/unified_scheduler_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/scripts/start_unified_scheduler.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/scripts/start_unified_scheduler.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/server/unified_data_scheduler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/action.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/action.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/chat.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/chat.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/comment_sum.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/comment_sum.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/gag_summarize.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/gag_summarize.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/group_chat.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/group_chat.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/profile_gen.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/profile_gen.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/qa_generate.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/qa_generate.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/reasoning_of_fans.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/reasoning_of_fans.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/actions/relationship_summarize.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/actions/relationship_summarize.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/app.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/base/dataclasses.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/base/dataclasses.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/base/memory.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/base/memory.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/chat_template.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/chat_template.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/chat_template_old.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/chat_template_old.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/describe_behavioral_tendencies.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/describe_behavioral_tendencies.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/describe_personality.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/describe_personality.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/describe_talk_habits.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/describe_talk_habits.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/describe_tone.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/describe_tone.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/gag_gen.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/gag_gen.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/groupchat_template.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/groupchat_template.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/livestream_chat.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/qa_extract.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/qa_extract.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/reduce_summary.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/reduce_summary.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/web_info_eg.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/web_info_eg.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/prompts/web_info_eg_clean.txt" beforeDir="false" afterPath="$PROJECT_DIR$/algos/prompts/web_info_eg_clean.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/provider/llm/hunyuan.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/provider/llm/hunyuan.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/provider/llm/hunyuan_new.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/provider/llm/hunyuan_new.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/provider/llm/yi.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/provider/llm/yi.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/provider/model_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/algos/provider/model_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/user_data_scheduler.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/user_data_scheduler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/user_data_till_server.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/till_server.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/bvlist_collector.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/bvlist_collector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/cache_from_vtbs.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/tools/cache_from_vtbs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/discuss_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/tools/discuss_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/fetch_comments.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/tools/fetch_comments.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/fetch_uka_api.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/fetch_uka_api.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/fetch_up_image.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/fetch_up_image.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/fetch_video_info_tools.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/tools/fetch_video_info_tools.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/query_board_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/query_board_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/query_creator_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/query_creator_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/query_live_info.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/query_live_info.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/query_user_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/query_user_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/query_web_user_info.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/query_web_user_info.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/retrieve_info.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/topic_fetch.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/tools/topic_fetch.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/tools/web_search.py" beforeDir="false" afterPath="$PROJECT_DIR$/backend/tools/web_search.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/cookie_pool.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/data_refractor.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/data_refractor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/db_pool.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/db_pool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/extract_cookie.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/extract_cookie.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/file_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/file_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/fonts/SmileySans-Oblique.ttf" beforeDir="false" afterPath="$PROJECT_DIR$/utils/fonts/SmileySans-Oblique.ttf" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/fonts/msyh.ttf" beforeDir="false" afterPath="$PROJECT_DIR$/utils/fonts/msyh.ttf" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/json_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/json_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/stop_words_zh.txt" beforeDir="false" afterPath="$PROJECT_DIR$/utils/stop_words_zh.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/tieba_crawl.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/tieba_crawl.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/vtuber_name_to_file.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/vtuber_name_to_file.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/word_cloud_gen.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/word_cloud_gen.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/utils/word_count.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/word_count.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/room.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/config/target.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/config/whole.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/const_example.py" beforeDir="false" afterPath="$PROJECT_DIR$/const_example.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/CREATOR_SCHEDULER_README.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/CREATOR.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fix/add_pubtime_column.py" beforeDir="false" afterPath="$PROJECT_DIR$/fix/add_pubtime_column.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fix/analyze_video_stats.py" beforeDir="false" afterPath="$PROJECT_DIR$/fix/analyze_video_stats.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fix/fix_video_stats.py" beforeDir="false" afterPath="$PROJECT_DIR$/fix/fix_video_stats.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fix/quick_fix_existing_videos.py" beforeDir="false" afterPath="$PROJECT_DIR$/fix/quick_fix_existing_videos.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/requirements.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/creator_info_scheduler_main.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/creator_info_scheduler_main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/creator_info_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/gate/creator_info_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/creator_scheduler_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/base/creator_scheduler_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/creator_scheduler_control.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/base/creator_scheduler_control.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/live_info_till_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/gate/live_info_till_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/till_main.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/server/user_info_till_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/gate/user_info_till_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/sql_insert.py" beforeDir="false" afterPath="$PROJECT_DIR$/sql/sql_insert.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/backend/action/test_base_action.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/backend/action/test_base_action.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/backend/action/test_gen_comment_topics.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/backend/action/test_gen_comment_topics.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/backend/action/test_relationship_summarize.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/backend/action/test_relationship_summarize.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/backend/action/test_summarise_rise_reason.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/backend/action/test_summarise_rise_reason.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/test_creator_scheduler.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="31GrZ3mYjYvAKqRZrr9550sxjcR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\gitrepo\vupbi\algos" />
      <recent name="E:\gitrepo\vupbi\utils" />
      <recent name="E:\gitrepo\vupbi\logs" />
      <recent name="E:\gitrepo\vupbi\server\base" />
      <recent name="E:\gitrepo\vupbi\server\gate" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.17890.14" />
        <option value="bundled-python-sdk-5b207ade9991-7e9c3bbb6e34-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.17890.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5d038f75-6280-47d5-ac96-4a74e9cd67e4" name="更改" comment="" />
      <created>1755161303877</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755161303877</updated>
      <workItem from="1755161305009" duration="4963000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>