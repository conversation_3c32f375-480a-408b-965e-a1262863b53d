# Creator Info Server 定时调度系统

基于 `till_main.py` 的设计模式，为 `creator_info_server.py` 创建的定时调度系统。

## 功能特性

- ✅ **自动化执行**: 每天自动运行所有业务函数
- ✅ **错误处理**: 单个函数失败不影响其他函数执行
- ✅ **日志记录**: 详细的执行日志和状态记录
- ✅ **进程监控**: 自动重启机制，确保服务稳定运行
- ✅ **配置管理**: 灵活的配置文件支持
- ✅ **控制接口**: 启动、停止、状态查询等控制功能

## 文件结构

```
server/
├── creator_info_server.py              # 原始业务服务器（已增强）
├── creator_info_scheduler_main.py      # 调度器主程序
├── creator_scheduler_config.py         # 配置文件
├── creator_scheduler_control.py        # 控制脚本
└── CREATOR_SCHEDULER_README.md         # 本文档
```

## 业务函数分类

### Overview 概览数据
- `fetch_overview_stat()` - 获取概览统计数据
- `fetch_attention_analyze()` - 涨粉分析
- `fetch_archive_analyze()` - 播放分析
- `fetch_video_overview()` - 视频概览

### Fans 粉丝数据
- `fetch_fan_graph()` - 粉丝数据图表
- `fetch_fan_overview()` - 粉丝概览

### Video 视频数据
- `fetch_video_compare()` - 视频数据比较
- `fetch_video_pandect()` - 视频数据增量趋势
- `fetch_video_survey()` - 稿件操作来源占比
- `fetch_video_source()` - 稿件操作来源占比情况
- `fetch_video_view_data()` - 播放分布情况

## 快速开始

### 1. 启动调度器

```bash
# 使用默认配置启动（每天凌晨2点执行）
python creator_scheduler_control.py start

# 自定义执行时间（每天上午8点30分执行）
python creator_scheduler_control.py start --hour 8 --minute 30

# 指定用户UID
python creator_scheduler_control.py start --uid 401315430 --hour 2 --minute 0
```

### 2. 查看状态

```bash
# 查看调度器状态
python creator_scheduler_control.py status

# 查看日志
python creator_scheduler_control.py logs

# 查看最近100行日志
python creator_scheduler_control.py logs --lines 100
```

### 3. 停止调度器

```bash
# 停止调度器
python creator_scheduler_control.py stop

# 重启调度器
python creator_scheduler_control.py restart
```

### 4. 测试运行

```bash
# 立即执行一次所有任务（用于测试）
python creator_info_scheduler_main.py --mode once
```

## 配置说明

### 调度配置 (`creator_scheduler_config.py`)

```python
SCHEDULER_CONFIG = {
    'default_daily_hour': 2,        # 默认执行小时
    'default_daily_minute': 0,      # 默认执行分钟
    'function_delay': 5,            # 函数间延迟（秒）
    'category_delay': 10,           # 分类间延迟（秒）
    'max_retries': 3,               # 最大重试次数
}
```

### 业务函数配置

```python
BUSINESS_FUNCTIONS_CONFIG = {
    'overview': {
        'functions': [...],
        'enabled': True,            # 是否启用此分类
        'priority': 1               # 执行优先级
    }
}
```

### 特殊函数参数

```python
SPECIAL_FUNCTIONS_CONFIG = {
    'fetch_archive_analyze': {
        'params': {'period': 0}     # 函数参数
    }
}
```

## 日志系统

日志文件位置: `logs/creator_scheduler.log`

日志级别:
- `INFO`: 正常执行信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

## 监控和维护

### 进程监控
- 自动检测进程状态
- 异常退出时自动重启
- 支持优雅停止和强制停止

### 状态文件
- `creator_scheduler.pid`: 进程ID文件
- `creator_scheduler_status.json`: 状态信息文件

### 健康检查
```bash
# 检查调度器是否正常运行
python creator_scheduler_control.py status

# 查看系统资源使用情况
ps aux | grep creator_scheduler
```

## 集成到现有服务

### 作为独立服务运行
```bash
# 后台运行
nohup python creator_info_scheduler_main.py --mode monitor > scheduler.log 2>&1 &
```

### 集成到系统服务
可以创建 systemd 服务文件或使用其他进程管理工具。

## 故障排除

### 常见问题

1. **调度器无法启动**
   - 检查数据库连接
   - 检查 B站 凭证配置
   - 查看错误日志

2. **函数执行失败**
   - 检查网络连接
   - 检查 API 限制
   - 查看具体错误信息

3. **进程意外退出**
   - 查看系统日志
   - 检查内存使用情况
   - 检查权限设置

### 调试模式

```bash
# 单次执行用于调试
python creator_info_scheduler_main.py --mode once

# 查看详细日志
tail -f logs/creator_scheduler.log
```

## 扩展功能

### 添加新的业务函数
1. 在 `creator_info_server.py` 中添加新函数
2. 在 `creator_scheduler_config.py` 中配置函数分类
3. 重启调度器

### 自定义调度时间
1. 修改 `SCHEDULE_JOBS_CONFIG` 配置
2. 支持多个调度任务
3. 支持不同函数的不同执行频率

## 性能优化

- 合理设置函数间延迟，避免API限制
- 监控内存使用，及时处理内存泄漏
- 定期清理日志文件
- 优化数据库连接池配置

## 安全考虑

- 保护 B站 凭证信息
- 限制日志文件访问权限
- 定期更新依赖包
- 监控异常访问模式
