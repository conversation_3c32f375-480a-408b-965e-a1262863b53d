#!/usr/bin/env python3
"""
Test script to verify the fixes for:
1. get_nested_value function runtime error
2. Cookie configuration migration
"""

def test_get_nested_value():
    """Test the fixed get_nested_value function"""
    
    def get_nested_value(d, keys, default=None):
        for key in keys:
            if isinstance(d, dict):
                d = d.get(key)
            else:
                return default
            if d is None:
                return default
        return d
    
    print("Testing get_nested_value function:")
    
    # Test with dictionary (should work)
    test_dict = {'stat': {'like': 100, 'share': 50}}
    result1 = get_nested_value(test_dict, ['stat', 'like'])
    print(f"  Dict test: {result1} (expected: 100)")
    
    # Test with list (should return default, not crash)
    test_list = [1, 2, 3]
    result2 = get_nested_value(test_list, ['stat', 'like'], 'default')
    print(f"  List test: {result2} (expected: 'default')")
    
    # Test with None
    result3 = get_nested_value(None, ['stat', 'like'], 'default')
    print(f"  None test: {result3} (expected: 'default')")
    
    # Test with mixed types
    test_mixed = {'data': [1, 2, 3]}
    result4 = get_nested_value(test_mixed, ['data', 'like'], 'default')
    print(f"  Mixed test: {result4} (expected: 'default')")
    
    print("✓ get_nested_value function tests passed!")

def test_cookie_manager():
    """Test cookie manager functionality"""
    import json
    import os
    
    print("\nTesting cookie manager:")
    
    # Check if cookie_config.json exists
    config_path = "server/config/cookie_config.json"
    if os.path.exists(config_path):
        print(f"  ✓ Cookie config file exists: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Check structure
            if 'cookies' in config:
                print("  ✓ Config has 'cookies' section")
                
                for task_type in ['user', 'creator', 'live']:
                    if task_type in config['cookies']:
                        cookie_data = config['cookies'][task_type]
                        enabled = cookie_data.get('enabled', False)
                        has_sessdata = bool(cookie_data.get('SESSDATA'))
                        print(f"    {task_type}: enabled={enabled}, has_SESSDATA={has_sessdata}")
                    else:
                        print(f"    {task_type}: not found")
            else:
                print("  ✗ Config missing 'cookies' section")
                
        except Exception as e:
            print(f"  ✗ Error reading config: {e}")
    else:
        print(f"  ✗ Cookie config file not found: {config_path}")

if __name__ == "__main__":
    print("Running fix verification tests...\n")
    
    test_get_nested_value()
    test_cookie_manager()
    
    print("\n✓ All tests completed!")
