"""
Server Base Module
服务器基础模块

Contains core functionality for the server package including:
- Cookie management
- Configuration management
- Scheduler control

包含服务器包的核心功能：
- Cookie管理
- 配置管理
- 调度器控制
"""

# Import main components
try:
    from .cookie_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_cookie_manager, get_task_cookie
    COOKIE_MANAGER_AVAILABLE = True
except ImportError:
    COOKIE_MANAGER_AVAILABLE = False
    CookieManager = None
    get_cookie_manager = None
    get_task_cookie = None

try:
    from .unified_scheduler_config import (
        load_unified_config,
        get_enabled_data_types,
        get_data_type_functions,
        get_data_type_schedule,
        get_scheduler_config
    )
    UNIFIED_CONFIG_AVAILABLE = True
except ImportError:
    UNIFIED_CONFIG_AVAILABLE = False

try:
    from .creator_scheduler_config import (
        get_enabled_functions,
        get_scheduler_config as get_creator_scheduler_config
    )
    CREATOR_CONFIG_AVAILABLE = True
except ImportError:
    CREATOR_CONFIG_AVAILABLE = False

# Export available components
__all__ = []

if COOKIE_MANAGER_AVAILABLE:
    __all__.extend([
        "CookieManager",
        "get_cookie_manager", 
        "get_task_cookie"
    ])

if UNIFIED_CONFIG_AVAILABLE:
    __all__.extend([
        "load_unified_config",
        "get_enabled_data_types",
        "get_data_type_functions", 
        "get_data_type_schedule",
        "get_scheduler_config"
    ])

if CREATOR_CONFIG_AVAILABLE:
    __all__.extend([
        "get_enabled_functions",
        "get_creator_scheduler_config"
    ])
