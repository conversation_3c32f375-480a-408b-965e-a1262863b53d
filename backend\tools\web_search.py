import time
import re
import requests
import pandas as pd
import os
from bs4 import BeautifulSoup
from logger import logger
from const import PROJECT_ROOT

# Debug only
# https://search.bilibili.com/all?keyword=%E5%85%89%E5%8F%94%E9%94%90%E8%AF%84&from_source=webtop_search&spm_id_from=333.999&search_source=5&pubtime_begin_s=1727539200&pubtime_end_s=1727625599 20240928-20240929 86,399

# https://space.bilibili.com/13490123/search/video?keyword=%E4%BF%8F%E9%B8%A1%E4%BC%A0%E8%AF%B4

# search by user:
# /html/body/div[2]/div[4]/div/div[2]/div/div[2]/div[4]/div/div/ul[2]
# #submit-video-list > ul.clearfix.cube-list #selector
# document.querySelector("#submit-video-list > ul.clearfix.cube-list") js_path

# /html/body/div[3]/div/div[2]/div[2]/div/div/div/div[2]/div/div[1]/div/div[2]/div


class BiliSearch:

    def __init__(self):
        self.url = "https://api.bilibili.com/x/web-interface/wbi/search/type"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        }
        self.output_dir = f"{PROJECT_ROOT}/output/searching/"
        os.makedirs(self.output_dir, exist_ok=True)

    def trans_data(self, timeStamp):
        """
        date trans.
        """
        timeArray = time.localtime(timeStamp)
        otherStyleTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
        return otherStyleTime

    def search(self, keyword, max_page):
        output_path = f"{self.output_dir}/{keyword}_{max_page}.csv"
        if os.path.exists(output_path):
            df = pd.read_csv(output_path, index_col=0)
            res_list = df.to_dict(orient="records")
            return res_list
        else:
            collected_data = []
            for page in range(1, max_page + 1):
                logger.info(f"Crawl with {page} page...")
                params = {
                    "search_type": "video",
                    "keyword": keyword,
                    "page": page,
                }
                try:
                    r = requests.get(self.url, headers=self.headers, params=params)
                    if r.status_code == 200:
                        j_data = r.json()
                        data_list = j_data["data"]["result"]
                        logger.info(f"Data Length: {len(data_list)}")

                        for data in data_list:
                            title = re.compile(r"<[^>]+>", re.S).sub("", data["title"])
                            collected_data.append(
                                {
                                    "标题": title,
                                    "作者": data["author"],
                                    "bvid": data["bvid"],
                                    "上传时间": self.trans_data(data["pubdate"]),
                                    "视频时长": data["duration"],
                                    "弹幕数": data.get(
                                        "video_review", 0
                                    ),  # 如果键不存在，则默认为0
                                    "点赞数": data.get(
                                        "like", 0
                                    ),  # 如果键'like'不存在，这里您需要确认正确的键或逻辑
                                    "播放量": data["play"],
                                    "收藏量": data["favorites"],
                                    "分区类型": data["typename"],
                                    "标签": data["tag"],
                                    "描述": data["description"],
                                }
                            )
                    else:
                        logger.info(f"Failed with code: {r.status_code}")
                except Exception as e:
                    logger.info("Error:", e)

                time.sleep(1)

            df = pd.DataFrame(collected_data)
            with open(output_path, "w", encoding="utf-8-sig", newline="") as f:
                df.to_csv(f, index=True, header=f.tell() == 0)

            logger.info(f"Complete with {page} page.")
            return collected_data


def fetch_web_info_with_jina(input_url_or_str: str):
    if input_url_or_str.startswith("https://"):
        in_url = input_url_or_str
    else:
        in_url = f"https://search.bilibili.com/all?keyword={input_url_or_str}"

    # TODO
    # &pubtime_begin_s=1727539200&pubtime_end_s=1727625599

    url = "https://r.jina.ai/" + in_url
    headers = {
        "Authorization": "Bearer jina_0b17a0f4f68049df84ac1e4fec58aa82vgSuBGLbB-DDklvv5vyM0kOFfvd6"
    }

    response = requests.get(url, headers=headers)
    return response.text


def get_clearfix_cube_list_inner_text(input_url_or_str):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
    }
    if input_url_or_str.startswith("https://"):
        in_url = input_url_or_str
    else:
        in_url = f"https://space.bilibili.com/13490123/search/video?keyword={input_url_or_str}"
    response = requests.get(in_url, headers=headers)
    response.raise_for_status()

    soup = BeautifulSoup(response.text, "html.parser")

    elements = soup.select("#submit-video-list > ul.clearfix.cube-list")

    response = ""
    for element in elements:
        inner_text = element.get_text(strip=True)
        print(inner_text)
        response += inner_text + "\n"


# get_clearfix_cube_list_inner_text("俏鸡传说")

if __name__ == "__main__":
    import time

    t1 = time.time()
    search_keyword = "光叔调侃EZI：运气是强者的自谦"
    max_page = 2
    biliSearch = BiliSearch()
    a = biliSearch.search(search_keyword, max_page)
    t2 = time.time()
    logger.info(a)
    logger.info(f"Time: {t2-t1}")
