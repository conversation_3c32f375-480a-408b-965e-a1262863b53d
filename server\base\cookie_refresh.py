"""
Cookie Refresh Module

Implements Bilibili cookie refresh mechanism based on official API documentation
"""

import asyncio
import time
import json
import re
from typing import Dict, Optional, <PERSON>ple
import aiohttp
from logger import logger

try:
    from Crypto.Cipher import PKCS1_OAEP
    from Crypto.PublicKey import RSA
    from Crypto.Hash import SHA256
    import binascii
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    logger.warning("pycryptodome not installed, cookie refresh will not work")


class BilibiliCookieRefresher:
    """
    Bilibili Cookie Refresher
    B站Cookie刷新器
    """
    
    # RSA public key for CorrespondPath generation
    RSA_PUBLIC_KEY = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLgd2OAkcGVtoE3ThUREbio0Eg
Uc/prcajMKXvkCKFCWhJYJcLkcM2DKKcSeFpD/j6Boy538YXnR6VhcuUJOhH2x71
nzPjfdTcqMz7djHum0qSZA0AyCBDABUqCrfNgCiJ00Ra7GmRj+YCK1NJEuewlb40
JNrRuoEUXpabUzGB8QIDAQAB
-----END PUBLIC KEY-----'''

    def __init__(self):
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def generate_correspond_path(self, timestamp: int) -> str:
        """
        Generate CorrespondPath using RSA-OAEP encryption
        
        Args:
            timestamp: Current millisecond timestamp
            
        Returns:
            Encrypted correspond path as hex string
        """
        if not CRYPTO_AVAILABLE:
            raise RuntimeError("pycryptodome is required for cookie refresh")
            
        try:
            key = RSA.importKey(self.RSA_PUBLIC_KEY)
            cipher = PKCS1_OAEP.new(key, SHA256)
            message = f'refresh_{timestamp}'.encode()
            encrypted = cipher.encrypt(message)
            return binascii.b2a_hex(encrypted).decode()
        except Exception as e:
            logger.error(f"Error generating correspond path: {e}")
            raise

    async def check_refresh_needed(self, cookie_str: str) -> Tuple[bool, Optional[int]]:
        """
        Check if cookie needs refresh
        
        Args:
            cookie_str: Cookie string
            
        Returns:
            Tuple of (needs_refresh, timestamp)
        """
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        try:
            # Extract bili_jct from cookie
            bili_jct = self._extract_bili_jct(cookie_str)
            
            url = "https://passport.bilibili.com/x/passport-login/web/cookie/info"
            params = {"csrf": bili_jct} if bili_jct else {}
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                "Cookie": cookie_str
            }
            
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"Failed to check refresh status: HTTP {response.status}")
                    return False, None
                    
                data = await response.json()
                if data.get("code") != 0:
                    logger.error(f"Failed to check refresh status: {data.get('message')}")
                    return False, None
                    
                result_data = data.get("data", {})
                needs_refresh = result_data.get("refresh", False)
                timestamp = result_data.get("timestamp")
                
                logger.info(f"Cookie refresh check: needs_refresh={needs_refresh}, timestamp={timestamp}")
                return needs_refresh, timestamp
                
        except Exception as e:
            logger.error(f"Error checking refresh status: {e}")
            return False, None

    async def get_refresh_csrf(self, correspond_path: str, cookie_str: str) -> Optional[str]:
        """
        Get refresh_csrf token
        
        Args:
            correspond_path: Generated correspond path
            cookie_str: Cookie string
            
        Returns:
            refresh_csrf token or None if failed
        """
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        try:
            url = f"https://www.bilibili.com/correspond/1/{correspond_path}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                "Cookie": cookie_str
            }
            
            async with self.session.get(url, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"Failed to get refresh_csrf: HTTP {response.status}")
                    return None
                    
                html = await response.text()
                
                # Extract refresh_csrf from HTML
                match = re.search(r'<div id="1-name">([^<]+)</div>', html)
                if match:
                    refresh_csrf = match.group(1)
                    logger.info(f"Got refresh_csrf: {refresh_csrf}")
                    return refresh_csrf
                else:
                    logger.error("Could not extract refresh_csrf from response")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting refresh_csrf: {e}")
            return None

    async def refresh_cookie(self, refresh_token: str, refresh_csrf: str, cookie_str: str) -> Optional[Dict]:
        """
        Refresh cookie using refresh_token and refresh_csrf
        使用refresh_token和refresh_csrf刷新Cookie
        
        Args:
            refresh_token: Persistent refresh token
            refresh_csrf: Real-time refresh token
            cookie_str: Current cookie string
            
        Returns:
            Dict with new cookie info or None if failed
        """
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        try:
            bili_jct = self._extract_bili_jct(cookie_str)
            if not bili_jct:
                logger.error("Could not extract bili_jct from cookie")
                return None
                
            url = "https://passport.bilibili.com/x/passport-login/web/cookie/refresh"
            data = {
                "csrf": bili_jct,
                "refresh_csrf": refresh_csrf,
                "source": "main_web",
                "refresh_token": refresh_token
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                "Cookie": cookie_str,
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            async with self.session.post(url, data=data, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"Failed to refresh cookie: HTTP {response.status}")
                    return None
                    
                result = await response.json()
                if result.get("code") != 0:
                    logger.error(f"Failed to refresh cookie: {result.get('message')}")
                    return None
                    
                # Extract new cookies from response headers
                new_cookies = {}
                for header_name, header_value in response.headers.items():
                    if header_name.lower() == 'set-cookie':
                        cookie_parts = header_value.split(';')[0].split('=', 1)
                        if len(cookie_parts) == 2:
                            new_cookies[cookie_parts[0]] = cookie_parts[1]
                
                result_data = result.get("data", {})
                new_refresh_token = result_data.get("refresh_token")
                
                logger.info("Cookie refresh successful")
                return {
                    "cookies": new_cookies,
                    "refresh_token": new_refresh_token
                }
                
        except Exception as e:
            logger.error(f"Error refreshing cookie: {e}")
            return None

    async def confirm_refresh(self, old_refresh_token: str, new_cookie_str: str) -> bool:
        """
        Confirm cookie refresh to invalidate old refresh_token
        确认Cookie刷新以使旧的refresh_token失效
        
        Args:
            old_refresh_token: Old refresh token to invalidate
            new_cookie_str: New cookie string
            
        Returns:
            True if successful, False otherwise
        """
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        try:
            new_bili_jct = self._extract_bili_jct(new_cookie_str)
            if not new_bili_jct:
                logger.error("Could not extract bili_jct from new cookie")
                return False
                
            url = "https://passport.bilibili.com/x/passport-login/web/confirm/refresh"
            data = {
                "csrf": new_bili_jct,
                "refresh_token": old_refresh_token
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                "Cookie": new_cookie_str,
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            async with self.session.post(url, data=data, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"Failed to confirm refresh: HTTP {response.status}")
                    return False
                    
                result = await response.json()
                if result.get("code") != 0:
                    logger.error(f"Failed to confirm refresh: {result.get('message')}")
                    return False
                    
                logger.info("Cookie refresh confirmed")
                return True
                
        except Exception as e:
            logger.error(f"Error confirming refresh: {e}")
            return False

    def _extract_bili_jct(self, cookie_str: str) -> Optional[str]:
        """Extract bili_jct from cookie string"""
        for item in cookie_str.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                if key == 'bili_jct':
                    return value
        return None

    async def full_refresh_process(self, cookie_data: Dict) -> Optional[Dict]:
        """
        Execute complete cookie refresh process
        执行完整的Cookie刷新流程
        
        Args:
            cookie_data: Cookie data dict with all fields
            
        Returns:
            Updated cookie data or None if failed
        """
        try:
            # Build current cookie string
            cookie_str = self._build_cookie_string(cookie_data)
            
            # Check if refresh is needed
            needs_refresh, timestamp = await self.check_refresh_needed(cookie_str)
            if not needs_refresh:
                logger.info("Cookie refresh not needed")
                return cookie_data
                
            if not timestamp:
                logger.error("No timestamp received for refresh")
                return None
                
            # Generate correspond path
            correspond_path = self.generate_correspond_path(timestamp)
            
            # Get refresh_csrf
            refresh_csrf = await self.get_refresh_csrf(correspond_path, cookie_str)
            if not refresh_csrf:
                return None
                
            # Refresh cookie
            refresh_result = await self.refresh_cookie(
                cookie_data.get("refresh_token", ""),
                refresh_csrf,
                cookie_str
            )
            if not refresh_result:
                return None
                
            # Update cookie data
            new_cookies = refresh_result["cookies"]
            new_refresh_token = refresh_result["refresh_token"]
            
            updated_data = cookie_data.copy()
            for key, value in new_cookies.items():
                if key in updated_data:
                    updated_data[key] = value
                    
            updated_data["refresh_token"] = new_refresh_token
            updated_data["last_refresh"] = int(time.time())
            
            # Build new cookie string
            new_cookie_str = self._build_cookie_string(updated_data)
            
            # Confirm refresh
            old_refresh_token = cookie_data.get("refresh_token", "")
            if old_refresh_token:
                await self.confirm_refresh(old_refresh_token, new_cookie_str)
            
            logger.info("Cookie refresh process completed successfully")
            return updated_data
            
        except Exception as e:
            logger.error(f"Error in full refresh process: {e}")
            return None

    def _build_cookie_string(self, cookie_data: Dict) -> str:
        """Build cookie string from cookie data"""
        parts = []
        for key in ["SESSDATA", "bili_jct", "buvid3", "buvid4", "DedeUserID", "b_nut", "sid"]:
            value = cookie_data.get(key)
            if value:
                parts.append(f"{key}={value}")
        return "; ".join(parts)
