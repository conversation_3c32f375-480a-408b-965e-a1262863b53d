import jieba
from wordcloud import WordCloud
import numpy as np
from PIL import Image
from const import PROJECT_ROOT
from utils import get_zh_role_name

STOPWORDS_ZH = set(
    map(
        str.strip,
        open(
            f"{PROJECT_ROOT}/backend/utils/stop_words_zh.txt", encoding="utf-8"
        ).readlines(),
    )
)


def word_cloud_gen(context_list: list, target_path: str, vtuber_name="xingtong"):

    char_zh = get_zh_role_name(vtuber_name)
    mask_image = np.array(
        Image.open(f"{PROJECT_ROOT}/data/images/vtubers/{char_zh}.jpg")
    )

    context = " ".join(context_list)
    words = jieba.lcut(context)
    newtxt = " ".join(words)

    wordcloud = WordCloud(
        width=800,
        height=800,
        background_color="white",
        mask=mask_image,
        stopwords=STOPWORDS_ZH,
        max_words=500,
        min_font_size=2,
        contour_width=0.5,
        font_path=f"{PROJECT_ROOT}/backend/utils/fonts/msyh.ttf",
        contour_color="yellow",
    ).generate(newtxt)

    wordcloud.to_file(target_path)
