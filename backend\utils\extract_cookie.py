# -*- coding: utf-8 -*-

cookie = """
buvid3=40463DB3-D75E-E22D-7F1C-D7B851AB2F9B95686infoc; b_nut=1755005295; _uuid=1072EF77A-A618-4228-7ADA-8CB43D2F9FAA96264infoc; CURRENT_QUALITY=0; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTUyNjQ0OTYsImlhdCI6MTc1NTAwNTIzNiwicGx0IjotMX0.1aicYOp7rgDJY_31OA8V0uqtw5NIPWtSXz6z8YGGHpw; bili_ticket_expires=1755264436; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; rpdid=|(k||Yuklklk0J'u~ll||lkm~; enable_web_push=DISABLE; SESSDATA=90bf24d6%2C1770557363%2C2d5dc%2A81CjDF7s8DfAn8ZAuyGcioeyttCPTnIZ2ZqIISgptp7_ySPQve6KIAB1kCqRSYFi4JpjcSVllEQ2VXVk9lRE83a2VmUm9lZkw2UXEwa2p0eUVaSThGek94aS13U2ZITG1nWHZLcVo4SFpNWEU2czRaNkpndFhGekN6Zmpocm95dnBmS1VVRlA4TURBIIEC; bili_jct=ca2cf783ddd3aaef8a6ffc11bd0547da; DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; theme-tip-show=SHOWED; theme-avatar-tip-show=SHOWED; sid=8cdn0ndc; fingerprint=c0a393d67aba850cdcb97506d85f2f29; buvid_fp_plain=undefined; buvid_fp=c0a393d67aba850cdcb97506d85f2f29; hit-dyn-v2=1; CURRENT_FNVAL=4048; b_lsid=103EBD585_198A7A1F2BB; bp_t_offset_224780442=1100888882053906432; home_feed_column=4; browser_resolution=1042-1364
"""


# cookie = """
# buvid3=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; b_nut=1725987880; _uuid=E104A4F5E-51B4-C8D2-7794-165EC102DDB8E81048infoc; enable_web_push=DISABLE; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; header_theme_version=CLOSE; rpdid=|(um|kmkJ|~Y0J'u~klm))|~); DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; hit-dyn-v2=1; LIVE_BUVID=AUTO9717315048529878; buvid_fp_plain=undefined; CURRENT_QUALITY=80; go-back-dyn=0; enable_feed_channel=ENABLE; SESSDATA=d3fbed55%2C1765168820%2Cdcb92%2A61CjA7J7XcYFzx-5zUzoNz5H9e3NYFrzTEFu6iPkVn6fROaIw1lg25SSje98YQupTTxU0SVkNwMV9jM25SbVVrZGhfZDYxWEo3dXNOMERFZ3Q0STMyeEY5VVhLZTlxOTQtZzBjb3pNdDFyc25GZWNHRFRobG5qeXhGQ21lbUVTZDMtc0dlY1VsbXFRIIEC; bili_jct=73b892971dfc1647574d073f474dbe2e; fingerprint=9c23685b4ff99eb4b022de7784c57446; timeMachine=0; sid=71e9ino0; share_source_origin=WEIXIN; bsource=share_source_weixinchat; b_lsid=1D3CF4F5_197681738F8; CURRENT_FNVAL=2000; home_feed_column=5; browser_resolution=3165-1598; buvid_fp=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; bp_t_offset_224780442=1077874815194365952; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTAwNTk4ODgsImlhdCI6MTc0OTgwMDYyOCwicGx0IjotMX0.QKaE_Er-rWQ0U0o5xcaLcF5Nb8kignoAVeTFb8vPD2E; bili_ticket_expires=1750059828
# """

# 需要提取的字段及其对应的cookie名
fields = {
    "SESSDATA": "SESSDATA",
    "bili_jct": "bili_jct",
    "buvid3": "buvid3",
    "buvid4": "buvid4",
    "b_nut": "b_nut",
    "sid": "sid",
    "DedeUserID": "DedeUserID",
}

# 解析cookie为字典
cookie_dict = {}
for item in cookie.split(";"):
    if "=" in item:
        k, v = item.strip().split("=", 1)
        cookie_dict[k] = v

# 提取并输出
output = []
for key, cookie_name in fields.items():
    value = cookie_dict.get(cookie_name, "")
    output.append(f'"{key}": "{value}",')

# 输出为text
result = "\n".join(output)
print(result)
