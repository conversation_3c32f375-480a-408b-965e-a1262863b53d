#!/usr/bin/env python3
"""
Test script to verify the creator info implementation
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server.creator_info_server import CreatorInfoServer
from backend.tools.query_creator_data import (
    query_latest_overview_stat_by_uid,
    query_creator_data_summary_by_uid
)
from logger import logger


async def test_creator_implementation():
    """Test the creator info implementation"""
    
    logger.info("Starting creator implementation test...")
    
    # Initialize the creator info server
    cis = CreatorInfoServer(uid="401315430")
    
    try:
        # Initialize database tables
        await cis.initialize_async()
        logger.info("✅ Database tables initialized successfully")
        
        # Test fetching overview statistics
        logger.info("Testing fetch_overview_stat...")
        overview_result = await cis.fetch_overview_stat()
        if overview_result and overview_result.get('code') == 0:
            logger.info("✅ fetch_overview_stat completed successfully")
        else:
            logger.warning("⚠️ fetch_overview_stat returned unexpected result")
        
        # Test query functions
        logger.info("Testing query functions...")
        
        # Test querying latest overview stat
        latest_stat = await query_latest_overview_stat_by_uid("401315430")
        if latest_stat:
            logger.info("✅ query_latest_overview_stat_by_uid returned data")
        else:
            logger.info("ℹ️ No overview stat data found (expected for new setup)")
        
        # Test creator data summary
        summary = await query_creator_data_summary_by_uid("401315430")
        if summary:
            logger.info("✅ query_creator_data_summary_by_uid completed successfully")
            logger.info(f"Summary keys: {list(summary.keys())}")
        else:
            logger.info("ℹ️ No summary data found (expected for new setup)")
        
        logger.info("🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(test_creator_implementation())
