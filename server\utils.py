"""
Server Package Utilities
服务器包工具函数

Internal utility functions for the server package.
服务器包的内部工具函数。
"""

import re
import json
import base64
import struct
import urllib.parse
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

def safe_int(value: Any) -> int:
    """
    Safely convert value to integer
    安全地将值转换为整数
    """
    if value is None:
        return 0
    try:
        return int(value)
    except (ValueError, TypeError):
        return 0

def safe_float(value: Any) -> float:
    """
    Safely convert value to float
    安全地将值转换为浮点数
    """
    if value is None:
        return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

def safe_str(value: Any) -> str:
    """
    Safely convert value to string
    安全地将值转换为字符串
    """
    if value is None:
        return ""
    try:
        return str(value)
    except (ValueError, TypeError):
        return ""

def get_nested_value(d: Dict[str, Any], keys: List[str], default: Any = None) -> Any:
    """
    Get nested value from dictionary using key path
    使用键路径从字典中获取嵌套值
    
    Args:
        d: Dictionary to search in
        keys: List of keys representing the path
        default: Default value if path not found
        
    Returns:
        Value at the specified path or default
    """
    for key in keys:
        if isinstance(d, dict):
            d = d.get(key)
        else:
            return default
        if d is None:
            return default
    return d

def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format datetime to string
    将日期时间格式化为字符串
    """
    return dt.strftime(format_str)

def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """
    Parse datetime string
    解析日期时间字符串
    """
    try:
        return datetime.strptime(dt_str, format_str)
    except (ValueError, TypeError):
        return None

def clean_text(text: str) -> str:
    """
    Clean text by removing extra whitespace and special characters
    清理文本，移除多余的空白字符和特殊字符
    """
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove control characters
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    return text

def extract_numbers(text: str) -> List[int]:
    """
    Extract all numbers from text
    从文本中提取所有数字
    """
    if not text:
        return []
    
    numbers = re.findall(r'\d+', text)
    return [int(num) for num in numbers]

def format_number(num: Union[int, float], precision: int = 2) -> str:
    """
    Format number with appropriate units (K, M, B)
    使用适当的单位格式化数字 (K, M, B)
    """
    if num < 1000:
        return str(int(num))
    elif num < 1000000:
        return f"{num/1000:.{precision}f}K"
    elif num < 1000000000:
        return f"{num/1000000:.{precision}f}M"
    else:
        return f"{num/1000000000:.{precision}f}B"

def parse_formatted_number(text: str) -> int:
    """
    Parse formatted number string (e.g., "1.2K" -> 1200)
    解析格式化的数字字符串
    """
    if not text:
        return 0
    
    text = text.strip().upper()
    
    # Extract number and unit
    match = re.match(r'([\d.]+)([KMB]?)', text)
    if not match:
        return 0
    
    num_str, unit = match.groups()
    
    try:
        num = float(num_str)
        
        if unit == 'K':
            return int(num * 1000)
        elif unit == 'M':
            return int(num * 1000000)
        elif unit == 'B':
            return int(num * 1000000000)
        else:
            return int(num)
    except ValueError:
        return 0

def validate_uid(uid: str) -> bool:
    """
    Validate user ID format
    验证用户ID格式
    """
    if not uid:
        return False
    
    # Check if it's a valid number
    try:
        int(uid)
        return True
    except ValueError:
        return False

def validate_bvid(bvid: str) -> bool:
    """
    Validate BV ID format
    验证BV ID格式
    """
    if not bvid:
        return False
    
    # BV ID should start with "BV" followed by 10 characters
    return bool(re.match(r'^BV[a-zA-Z0-9]{10}$', bvid))

def extract_bvid_from_url(url: str) -> Optional[str]:
    """
    Extract BV ID from Bilibili URL
    从B站URL中提取BV ID
    """
    if not url:
        return None
    
    # Match BV ID in URL
    match = re.search(r'BV[a-zA-Z0-9]{10}', url)
    return match.group(0) if match else None

def extract_uid_from_url(url: str) -> Optional[str]:
    """
    Extract UID from Bilibili URL
    从B站URL中提取UID
    """
    if not url:
        return None
    
    # Match UID in URL patterns
    patterns = [
        r'/space/(\d+)',
        r'uid=(\d+)',
        r'mid=(\d+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None

def base64_to_float_array(base64_data: str) -> List[float]:
    """
    Convert base64 encoded data to float array
    将base64编码的数据转换为浮点数组
    """
    try:
        byte_array = base64.b64decode(base64_data)
        float_array = []
        
        for i in range(0, len(byte_array), 4):
            if i + 4 <= len(byte_array):
                num = struct.unpack("!f", byte_array[i:i + 4])[0]
                float_array.append(num)
        
        return float_array
    except Exception:
        return []

def create_time_range(start_time: datetime, end_time: datetime, interval_minutes: int = 60) -> List[datetime]:
    """
    Create a list of datetime objects within a time range
    在时间范围内创建日期时间对象列表
    """
    time_points = []
    current_time = start_time
    
    while current_time <= end_time:
        time_points.append(current_time)
        current_time += timedelta(minutes=interval_minutes)
    
    return time_points

def calculate_time_diff(start_time: datetime, end_time: datetime) -> Dict[str, int]:
    """
    Calculate time difference in various units
    计算各种单位的时间差
    """
    diff = end_time - start_time
    
    return {
        "total_seconds": int(diff.total_seconds()),
        "total_minutes": int(diff.total_seconds() / 60),
        "total_hours": int(diff.total_seconds() / 3600),
        "total_days": diff.days,
        "hours": diff.seconds // 3600,
        "minutes": (diff.seconds % 3600) // 60,
        "seconds": diff.seconds % 60
    }

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to specified length
    将文本截断到指定长度
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by removing invalid characters
    通过移除无效字符来清理文件名
    """
    if not filename:
        return "untitled"
    
    # Remove invalid characters for filenames
    invalid_chars = r'[<>:"/\\|?*]'
    filename = re.sub(invalid_chars, '_', filename)
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip('. ')
    
    # Ensure filename is not empty
    if not filename:
        return "untitled"
    
    return filename

def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge multiple dictionaries
    合并多个字典
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size
    将列表分割成指定大小的块
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """
    Decorator for retrying function on exception
    异常重试装饰器
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        import asyncio
                        await asyncio.sleep(delay * (attempt + 1))
                    else:
                        raise last_exception

        return wrapper
    return decorator

def get_zh_role_name(char: str) -> str:
    """
    Get Chinese role name for character
    获取角色的中文名称

    Args:
        char: Character name

    Returns:
        Chinese name or original name if not found
    """
    # Default mapping for common VTuber names
    name_mapping = {
        "嘉然": "嘉然",
        "乃琳": "乃琳",
        "贝拉": "贝拉",
        "珈乐": "珈乐",
        "向晚": "向晚",
        "hanser": "hanser",
        "阿梓": "阿梓",
        "东雪莲": "東雪蓮",
        "泠鸢": "泠鸢",
        "雫るる": "雫るる"
    }

    return name_mapping.get(char, char)

def get_vtuber_list():
    """
    Get list of VTuber names
    获取VTuber名称列表
    """
    return ["嘉然", "乃琳", "贝拉", "珈乐", "向晚"]

def get_vup_uid_by_any_name(name: str) -> Optional[str]:
    """
    Get VTuber UID by any name (full name, short name, English name)
    通过任意名称获取VTuber的UID

    Args:
        name: VTuber name in any format

    Returns:
        UID string or None if not found
    """
    # Default UID mapping for common VTubers
    uid_mapping = {
        "嘉然": "672328094",
        "乃琳": "672346917",
        "贝拉": "672353429",
        "珈乐": "351609538",
        "向晚": "672342685",
        "hanser": "11073",
        "阿梓": "7706705",
        "东雪莲": "1437582453",
        "東雪蓮": "1437582453",
        "泠鸢": "282994",
        "雫るる": "477317922"
    }

    return uid_mapping.get(name)

def get_vup_room_id_by_any_name(name: str) -> Optional[str]:
    """
    Get VTuber live room ID by any name
    通过任意名称获取VTuber的直播间ID

    Args:
        name: VTuber name in any format

    Returns:
        Room ID string or None if not found
    """
    # Default room ID mapping for common VTubers
    room_mapping = {
        "嘉然": "22625025",
        "乃琳": "22625027",
        "贝拉": "22632424",
        "珈乐": "22634198",
        "向晚": "22625026",
        "hanser": "946441",
        "阿梓": "7706705",
        "东雪莲": "21593109",
        "東雪蓮": "21593109",
        "泠鸢": "21304638",
        "雫るる": "21452505"
    }

    return room_mapping.get(name)

def get_vup_uid_by_short_name(name: str) -> Optional[str]:
    """
    Get VTuber UID by short name (alias for get_vup_uid_by_any_name)
    通过短名称获取VTuber的UID
    """
    return get_vup_uid_by_any_name(name)
